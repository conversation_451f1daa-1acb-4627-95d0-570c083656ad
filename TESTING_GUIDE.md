# Testing Guide for Session Management Fixes

## Issues Fixed
1. **Redis Session Accumulation**: Multiple session keys created for same user
2. **UserAlreadyAuthenticatedException**: Multiple users couldn't sign in from different browsers

## Test Scenarios

### Test 1: Redis Session Cleanup
**Objective**: Verify that logout properly cleans up Redis session data

**Steps**:
1. Login with a user account
2. Check Redis keys: `redis-cli keys "session:*"` and `redis-cli keys "cognito:*"`
3. Note the session keys created
4. Logout
5. Check Redis keys again - the session keys should be removed
6. Login again with the same user
7. Verify only new session keys are created (no accumulation)

**Expected Result**: 
- After logout, Redis session and cognito keys are removed
- New login creates fresh keys without accumulation

### Test 2: Multiple Browser Login
**Objective**: Verify that multiple users can sign in from different browsers

**Steps**:
1. Open Browser 1 (e.g., Chrome)
2. Login with User A
3. Verify successful login
4. Open Browser 2 (e.g., Firefox) or Incognito mode
5. Login with User B (or same user)
6. Verify successful login without `UserAlreadyAuthenticatedException`

**Expected Result**: 
- Both logins should succeed
- No `UserAlreadyAuthenticatedException` error
- Each browser maintains its own session

### Test 3: Session Cleanup During Login
**Objective**: Verify that expired sessions are cleaned up during login

**Steps**:
1. Login and logout multiple times to create some session history
2. Check Redis for accumulated sessions: `redis-cli keys "session:*"`
3. Login again
4. Check server logs for cleanup messages
5. Verify expired sessions are cleaned up

**Expected Result**: 
- Cleanup messages in server logs
- Expired sessions removed from Redis
- Only active session remains

### Test 4: Manual Cleanup Script
**Objective**: Test the cleanup utility script

**Steps**:
1. Create some test sessions by logging in/out multiple times
2. Run dry-run: `node scripts/cleanup-redis-sessions.js --dry-run`
3. Review what would be cleaned
4. Run actual cleanup: `node scripts/cleanup-redis-sessions.js`
5. Verify sessions are cleaned up

**Expected Result**: 
- Script shows accurate preview in dry-run mode
- Actual cleanup removes expired sessions
- Active sessions are preserved

## Monitoring Commands

### Check Redis Session Data
```bash
# List all session keys
redis-cli keys "session:*"

# List all cognito keys  
redis-cli keys "cognito:*"

# Get session data
redis-cli get "session:SESSION_ID_HERE"

# Count total sessions
redis-cli eval "return #redis.call('keys', 'session:*')" 0
```

### Check Server Logs
Look for these log messages:
- `🔐 [AUTH] Logging out user with session:`
- `🗑️ [SESSION] Redis data cleared for session:`
- `🧹 [SESSION] Found X total sessions in Redis`
- `✅ [SESSION] Cleaned up X orphaned/expired sessions`
- `🔧 [AUTH] Cleared existing auth state to prevent conflicts`

### Test Different Scenarios
1. **Same user, multiple browsers**: Should work without conflicts
2. **Different users, multiple browsers**: Should work without conflicts  
3. **Rapid login/logout cycles**: Should not accumulate sessions
4. **OAuth login**: Should also clean up properly
5. **Session expiry**: Expired sessions should be cleaned up

## Expected Behavior After Fixes

### Login Process
1. Clears any existing Amplify auth state
2. Clears token storage for fresh authentication
3. Performs authentication
4. Creates new session in Redis
5. Cleans up expired sessions for the user
6. Sets session cookie

### Logout Process  
1. Retrieves current session ID
2. Signs out from Cognito
3. Removes session and cognito data from Redis using session ID
4. Clears all cookies
5. Redirects to signin page

### Redis State
- Only active sessions should exist in Redis
- Each session should have corresponding cognito data
- Expired sessions should be automatically cleaned up
- No accumulation of old session keys

## Troubleshooting

### If UserAlreadyAuthenticatedException Still Occurs
1. Check if `ensureAmplifyConfig()` is being called properly
2. Verify token storage is being cleared
3. Check server logs for auth state clearing messages

### If Sessions Still Accumulate
1. Verify logout is passing session ID to `clearSession()`
2. Check Redis connectivity
3. Review cleanup function logs
4. Run manual cleanup script

### Performance Monitoring
- Monitor Redis memory usage
- Track number of session keys over time
- Monitor cleanup function execution time
- Watch for any Redis connection errors
