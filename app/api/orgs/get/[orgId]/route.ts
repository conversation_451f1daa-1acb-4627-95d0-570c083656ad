import { NextRequest, NextResponse } from 'next/server';
import { externalClient } from '@/app/api/_utils/external-client';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orgId: string }> },
) {
  try {
    const { orgId } = await params;

    if (!orgId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    // Proxy to external API
    const response = await externalClient.get(`/orgs/get/${orgId}`);

    // Transform response to match expected format
    const org = response.data.organization.org.organization;
    const transformedData = {
      orgName: org.name,
      orgID: org._id,
      orgDescription: org.description,
      orgEmail: org.orgEmail,
      orgOwnerEmail: org.ownerEmail,
      orgLogo: org.logo,
      orgLastUpdate: org.updatedAt ? new Date(org.updatedAt).toLocaleDateString() : 'Never',
    };

    return NextResponse.json(transformedData);
  } catch (error: any) {
    console.error('❌ [API Gateway] Error fetching organization:', error);

    return NextResponse.json(
      {
        error: error.message || 'Failed to fetch organization',
        orgName: '',
        orgID: '',
        orgDescription: '',
        orgEmail: '',
        orgOwnerEmail: '',
        orgLogo: '',
        orgLastUpdate: 'Never',
      },
      { status: error.status || 500 },
    );
  }
}
