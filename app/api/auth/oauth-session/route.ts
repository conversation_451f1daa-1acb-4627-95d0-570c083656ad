import { NextRequest, NextResponse } from 'next/server';
import { createSession, setSessionCookie, setCognitoTokenCookies } from '@/lib/session';

/**
 * POST /api/auth/oauth-session
 * Creates a secure session from OAuth tokens
 * 
 * This endpoint is used by the OAuth callback page to create a session
 * after successful OAuth authentication with providers like Google.
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { accessToken, idToken, userId, email } = body;

    console.log('🔐 [API] Creating OAuth session:', {
      hasAccessToken: !!accessToken,
      hasIdToken: !!idToken,
      userId: userId,
      email: email,
    });

    // Validate required fields
    if (!userId || !email) {
      return NextResponse.json(
        { 
          error: 'Missing required fields: userId and email are required',
          success: false 
        },
        { status: 400 }
      );
    }

    // Validate that we have at least one token
    if (!accessToken && !idToken) {
      return NextResponse.json(
        { 
          error: 'At least one token (access or ID) is required',
          success: false 
        },
        { status: 400 }
      );
    }

    // Store Cognito tokens in secure HTTP-only cookies
    if (accessToken || idToken) {
      await setCognitoTokenCookies({
        accessToken,
        idToken,
      });

      console.log('🍪 [API] Cognito tokens stored in secure cookies');
    }

    // Create secure JWT session
    const sessionToken = await createSession({
      username: email,
      email: email,
      userId: userId,
    });

    await setSessionCookie(sessionToken);

    console.log('✅ [API] OAuth session created successfully');

    return NextResponse.json(
      {
        success: true,
        message: 'Session created successfully',
      },
      { 
        status: 200,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
          'Pragma': 'no-cache',
        },
      }
    );
  } catch (error) {
    console.error('❌ [API] OAuth session creation error:', error);

    return NextResponse.json(
      {
        error: 'Failed to create session',
        success: false,
      },
      { 
        status: 500,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
          'Pragma': 'no-cache',
        },
      }
    );
  }
}

/**
 * GET /api/auth/oauth-session
 * Returns current OAuth session status
 */
export async function GET(request: NextRequest) {
  try {
    const { getSession, getCognitoTokenCookies } = await import('@/lib/session');
    
    const session = await getSession();
    const tokens = await getCognitoTokenCookies();

    return NextResponse.json({
      success: true,
      session: {
        isAuthenticated: !!session,
        user: session ? {
          email: session.email,
          userId: session.userId,
          username: session.username,
        } : null,
      },
      tokens: {
        hasAccessToken: !!tokens.accessToken,
        hasIdToken: !!tokens.idToken,
      },
    });
  } catch (error) {
    console.error('❌ [API] OAuth session status error:', error);

    return NextResponse.json(
      {
        error: 'Failed to get session status',
        success: false,
      },
      { status: 500 }
    );
  }
}
