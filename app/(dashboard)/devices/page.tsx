'use client';

import { useState, useMemo } from 'react';
import { DeviceCard } from '@/components/device-card';
import { useAllDevices } from '@/hooks/use-api';
import { DeviceCardProps } from '@/types/device';
import { Input } from '@/components/ui/input';
import { Search, Loader2, Filter, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu';

export default function DevicesPage() {
  const { data: devices, isLoading } = useAllDevices();
  const [search, setSearch] = useState('');
  const [providerFilter, setProviderFilter] = useState<string>('All Providers');

  const providers = useMemo<string[]>(() => {
    return Array.from(new Set((devices || []).map((d: DeviceCardProps) => d.provider)));
  }, [devices]);

  const filteredDevices = useMemo(
    () =>
      (devices || []).filter((device: DeviceCardProps) => {
        const matchesName = device.name?.toLowerCase().includes(search.toLowerCase());
        const matchesProvider =
          providerFilter === 'All Providers' ||
          device.provider.toLowerCase() === providerFilter.toLowerCase();
        return matchesName && matchesProvider;
      }),
    [devices, search, providerFilter],
  );

  return (
    <div className="min-h-screen bg-[#18141f]">
      {/* Navbar provided by root layout */}

      <main className="max-w-[1400px] mx-auto px-6 py-8">
        {/* Header Section */}
        <div className="mb-12">
          <h1 className="text-4xl font-semibold text-white mb-3">Quantum Devices</h1>
          <p className="text-[#94a3b8] text-lg leading-relaxed">
            Browse and manage all available quantum devices in your organization.
          </p>
        </div>

        {/* Search & Filter Section */}
        <div className="mb-10 flex items-center space-x-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-[#6b7280] w-5 h-5 transition-colors duration-200" />
            <Input
              placeholder="Search devices by name..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-12 pr-4 py-3 bg-[#1f1b24] border-[#374151] text-white placeholder:text-[#6b7280] text-base rounded-lg focus:border-[#8a2be2] focus:ring-2 focus:ring-[#8a2be2]/20 transition-all duration-200"
            />
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                aria-label="Filter by provider"
                variant="outline"
                className="bg-[#1f1b24] border-[#374151] text-[#6b7280] hover:bg-[#374151] hover:text-white"
              >
                <Filter className="w-5 h-5 mr-2" />
                {providerFilter}
                <ChevronDown className="w-4 h-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-[#1f1b24] border-[#374151]">
              <DropdownMenuItem
                className="text-[#6b7280] hover:text-white hover:bg-[#374151]"
                onClick={() => setProviderFilter('All Providers')}
              >
                All Providers
              </DropdownMenuItem>
              {providers.map((prov, idx) => (
                <DropdownMenuItem
                  key={idx}
                  className="text-[#6b7280] hover:text-white hover:bg-[#374151]"
                  onClick={() => setProviderFilter(prov)}
                >
                  {prov}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Loading State */}
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-20">
            <Loader2 className="w-8 h-8 text-[#8a2be2] animate-spin mb-4" />
            <div className="text-[#6b7280] text-lg mb-2">Loading devices...</div>
            <p className="text-[#9ca3af] text-base">
              Please wait while we fetch your quantum devices
            </p>
          </div>
        ) : filteredDevices.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-20">
            <div className="text-[#6b7280] text-xl mb-2">No devices found</div>
            <p className="text-[#9ca3af] text-base">Try adjusting your search criteria</p>
          </div>
        ) : (
          <div className="space-y-8">
            {filteredDevices.map((device: DeviceCardProps) => (
              <div
                key={device.qbraid_id}
                className="group bg-[#262131] rounded-xl border border-[#374151] shadow-lg hover:shadow-2xl hover:shadow-[#8a2be2]/10 transition-all duration-300 hover:border-[#4b5563] hover:-translate-y-1"
              >
                <DeviceCard {...device} />
              </div>
            ))}
          </div>
        )}
      </main>
    </div>
  );
}
