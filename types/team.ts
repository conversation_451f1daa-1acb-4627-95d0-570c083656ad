// Team and organization-related types for use across the app

/**
 * Represents a team member/user.
 */
export interface TeamMember {
  name: string;
  email: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  role: string;
  status: 'Active' | 'Invited' | 'Deactivated';
  userCredits?: number;
  lastActive?: string;
}

/**
 * Represents organization information.
 */
export interface OrgInfo {
  orgName: string;
  orgID: string;
  orgDescription?: string;
  orgEmail?: string;
  orgOwnerEmail?: string;
  orgLogo?: string;
  orgLastUpdate?: string;
}
