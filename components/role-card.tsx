import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader } from '@/components/ui/card';

interface RoleCardProps {
  role: {
    name: string;
    description: string;
    permissions: string[];
    members: Array<{
      name: string;
      email: string;
      avatar?: string;
    }>;
  };
}

export function RoleCard({ role }: RoleCardProps) {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  return (
    <Card className="bg-[#262131] border-[#3b3b3b]">
      <CardHeader>
        <h3 className="text-white font-semibold text-lg">{role.name}</h3>
        <p className="text-[#94a3b8] text-sm">{role.description}</p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h4 className="text-white font-medium text-sm mb-2">Permissions</h4>
          <ul className="space-y-1">
            {role.permissions.map((permission, index) => (
              <li key={index} className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-[#94a3b8] rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-[#94a3b8] text-sm">{permission}</span>
              </li>
            ))}
          </ul>
        </div>

        <div>
          <h4 className="text-white font-medium text-sm mb-2">Current Members</h4>
          <div className="flex -space-x-2">
            {role.members.slice(0, 3).map((member, index) => (
              <Avatar
                key={member.email}
                className="w-8 h-8 border-2 border-[#262131]"
                title={member.email}
              >
                <AvatarImage src={member.avatar} title={member.email} />
                <AvatarFallback className="bg-[#8a2be2] text-white text-xs">
                  {getInitials(member.name)}
                </AvatarFallback>
              </Avatar>
            ))}
            {role.members.length > 3 && (
              <div className="w-8 h-8 rounded-full bg-[#3b3b3b] border-2 border-[#262131] flex items-center justify-center">
                <span className="text-[#94a3b8] text-xs">+{role.members.length - 3}</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
