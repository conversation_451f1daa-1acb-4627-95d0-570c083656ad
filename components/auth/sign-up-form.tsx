'use client';

import { useActionState, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Lock, Mail, User, AlertCircle, Eye, EyeOff, CheckCircle, Circle } from 'lucide-react';
import Link from 'next/link';
import { registerUser } from '@/app/(auth)/actions';
import { useCSRFToken, CSRFTokenInput } from '@/lib/csrf';
import { AuthResult } from '@/types/auth';
import { GoogleSignInButton } from '@/components/auth/google-sign-in-button';
import { useAuth } from './auth-provider';
import { usePasswordValidation } from '@/hooks/use-password-validation';

const initialState: AuthResult = {
  success: false,
  error: undefined,
  nextStep: undefined,
  email: undefined,
};

export function SignUpForm() {
  const [state, formAction] = useActionState(registerUser, initialState);
  const { csrfToken, loading: csrfLoading, error: csrfError } = useCSRFToken();
  const router = useRouter();
  const { handleAuthSuccess } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // Use custom hook for password validation (no useEffect)
  const passwordValidation = usePasswordValidation(password, confirmPassword);

  // Handle navigation on success with useEffect to avoid render-time router updates
  useEffect(() => {
    if (state.success && state.nextStep === 'verify' && state.email) {
      const verifyUrl = `/verify?email=${encodeURIComponent(state.email)}`;
      router.push(verifyUrl);
    }
  }, [state.success, state.nextStep, state.email, router]);

  const displayError = state.error || csrfError;

  return (
    <div className="w-full bg-gradient-to-br from-[#2a1f3d] via-[#1f1727] to-[#1a1420] rounded-3xl shadow-2xl border border-purple-800/20 backdrop-blur-sm">
      <div className="p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center w-16 h-16 rounded-2xl   mb-6 mx-auto ">
            <img src="/qbraid_logo.png" alt="qBraid" className="h-16 w-auto opacity-90" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">Join qBraid</h1>
          <p className="text-slate-400 text-sm">Start building the future with quantum computing</p>
        </div>

        {/* Form */}
        <form action={formAction} className="space-y-5">
          <CSRFTokenInput csrfToken={csrfToken} />

          {/* Error Alert */}
          {displayError && (
            <div className="flex items-start gap-3 p-4 rounded-xl bg-red-500/10 border border-red-500/20 backdrop-blur-sm">
              <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
              <p className="text-red-400 text-sm">{displayError}</p>
            </div>
          )}

          {/* Full Name */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-slate-300 block">Full Name</label>
            <div className="relative group">
              <User className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-slate-500 group-focus-within:text-purple-400 transition-colors" />
              <input
                name="name"
                type="text"
                placeholder="Enter your full name"
                required
                className="w-full pl-10 pr-4 py-3.5 rounded-xl border border-slate-600/50 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500 bg-slate-800/50 text-white placeholder:text-slate-500 transition-all duration-200 hover:border-slate-500"
              />
            </div>
          </div>

          {/* Email */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-slate-300 block">Email Address</label>
            <div className="relative group">
              <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-slate-500 group-focus-within:text-purple-400 transition-colors" />
              <input
                name="email"
                type="email"
                placeholder="Enter your email"
                required
                className="w-full pl-10 pr-4 py-3.5 rounded-xl border border-slate-600/50 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500 bg-slate-800/50 text-white placeholder:text-slate-500 transition-all duration-200 hover:border-slate-500"
              />
            </div>
          </div>

          {/* Password */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-slate-300 block">Password</label>
            <div className="relative group">
              <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-slate-500 group-focus-within:text-purple-400 transition-colors" />
              <input
                name="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Create a strong password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                className="w-full pl-10 pr-12 py-3.5 rounded-xl border border-slate-600/50 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500 bg-slate-800/50 text-white placeholder:text-slate-500 transition-all duration-200 hover:border-slate-500"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-500 hover:text-slate-300 transition-colors"
              >
                {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>

            {/* Password Requirements */}
            {password && (
              <div className="space-y-2 p-3 bg-slate-800/30 rounded-lg border border-slate-700/50">
                <div className="flex items-center gap-2 text-xs">
                  {passwordValidation.minLength ? (
                    <CheckCircle className="w-3 h-3 text-green-400" />
                  ) : (
                    <Circle className="w-3 h-3 text-slate-500" />
                  )}
                  <span
                    className={passwordValidation.minLength ? 'text-green-400' : 'text-slate-400'}
                  >
                    At least 8 characters
                  </span>
                </div>
                <div className="flex items-center gap-2 text-xs">
                  {passwordValidation.hasNumber ? (
                    <CheckCircle className="w-3 h-3 text-green-400" />
                  ) : (
                    <Circle className="w-3 h-3 text-slate-500" />
                  )}
                  <span
                    className={passwordValidation.hasNumber ? 'text-green-400' : 'text-slate-400'}
                  >
                    Contains a number
                  </span>
                </div>
                <div className="flex items-center gap-2 text-xs">
                  {passwordValidation.hasSpecial ? (
                    <CheckCircle className="w-3 h-3 text-green-400" />
                  ) : (
                    <Circle className="w-3 h-3 text-slate-500" />
                  )}
                  <span
                    className={passwordValidation.hasSpecial ? 'text-green-400' : 'text-slate-400'}
                  >
                    Contains a special character
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Confirm Password */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-slate-300 block">Confirm Password</label>
            <div className="relative group">
              <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-slate-500 group-focus-within:text-purple-400 transition-colors" />
              <input
                name="confirmPassword"
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder="Confirm your password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
                className={`w-full pl-10 pr-12 py-3.5 rounded-xl border focus:outline-none focus:ring-2 focus:ring-purple-500/50 bg-slate-800/50 text-white placeholder:text-slate-500 transition-all duration-200 hover:border-slate-500 ${
                  confirmPassword && !passwordValidation.match
                    ? 'border-red-500/50 focus:border-red-500'
                    : confirmPassword && passwordValidation.match
                      ? 'border-green-500/50 focus:border-green-500'
                      : 'border-slate-600/50 focus:border-purple-500'
                }`}
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-500 hover:text-slate-300 transition-colors"
              >
                {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
            {confirmPassword && !passwordValidation.match && (
              <p className="text-red-400 text-xs flex items-center gap-1">
                <AlertCircle className="w-3 h-3" />
                Passwords don't match
              </p>
            )}
            {confirmPassword && passwordValidation.match && (
              <p className="text-green-400 text-xs flex items-center gap-1">
                <CheckCircle className="w-3 h-3" />
                Passwords match
              </p>
            )}
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={csrfLoading || !csrfToken || !passwordValidation.isFormValid}
            className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-semibold py-4 rounded-xl shadow-lg hover:shadow-xl hover:shadow-purple-500/25 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.01] active:scale-[0.99] mt-6 disabled:hover:scale-100"
          >
            {csrfLoading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                Loading...
              </div>
            ) : (
              'Create Account'
            )}
          </button>
        </form>

        {/* Divider */}
        <div className="flex items-center my-8">
          <div className="flex-1 border-t border-slate-600/50" />
          <span className="px-4 text-xs text-slate-500 font-medium">Or continue with</span>
          <div className="flex-1 border-t border-slate-600/50" />
        </div>

        {/* Social Buttons */}
        <div className="grid  gap-3 mb-8">
          <GoogleSignInButton />
        </div>

        {/* Sign In Link */}
        <p className="text-center text-sm text-slate-400">
          Already have an account?{' '}
          <Link
            href="/signin"
            className="text-purple-400 hover:text-purple-300 font-semibold hover:underline transition-colors"
          >
            Sign in
          </Link>
        </p>
        <p className="text-center text-sm text-slate-400 mt-10">
          &copy; 2025 qBraid Co. All rights reserved.
        </p>
      </div>
    </div>
  );
}
