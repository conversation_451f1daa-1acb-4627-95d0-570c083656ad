# Google OAuth Setup Instructions

## 🚨 Current Issue

Your app is missing the `NEXT_PUBLIC_COGNITO_DOMAIN` environment variable, which is required for Google OAuth to work.

## ✅ Quick Fix

Add this to your `.env.local` file:

```bash
# Add this line to your .env.local file
NEXT_PUBLIC_COGNITO_DOMAIN=your-cognito-domain.auth.us-east-2.amazoncognito.com
```

## 🔧 Complete Google OAuth Setup

### 1. AWS Cognito Configuration

1. **Go to AWS Cognito Console**
2. **Select your User Pool**: `us-east-2_bHQV6YVnt`
3. **App Integration Tab** → **Domain**
   - Create a Cognito domain (e.g., `qbraid-auth`)
   - Your domain will be: `qbraid-auth.auth.us-east-2.amazoncognito.com`

### 2. Google Cloud Console Setup

1. **Go to [Google Cloud Console](https://console.cloud.google.com/)**
2. **APIs & Services** → **Credentials**
3. **Create OAuth 2.0 Client ID**
   - Application type: Web application
   - Authorized redirect URIs:
     ```
     https://qbraid-auth.auth.us-east-2.amazoncognito.com/oauth2/idpresponse
     ```

### 3. Configure Cognito Identity Provider

1. **Back in AWS Cognito** → **Sign-in experience** → **Federated identity provider sign-in**
2. **Add identity provider** → **Google**
3. **Enter Google Client ID and Client Secret**
4. **Attribute mapping**:
   - Email → email
   - Name → name

### 4. Update Environment Variables

```bash
# Your current variables (already set)
NEXT_PUBLIC_COGNITO_USER_POOL_ID=us-east-2_bHQV6YVnt
NEXT_PUBLIC_COGNITO_CLIENT_ID=3aju3epc50421b4fp0bh75oj4u

# Add this for Google OAuth
NEXT_PUBLIC_COGNITO_DOMAIN=qbraid-auth.auth.us-east-2.amazoncognito.com
```

### 5. Test Google OAuth

1. **Restart your dev server**
2. **Go to /signup or /signin**
3. **Click the Google button**
4. **Should redirect to Google OAuth**

## 🔍 Troubleshooting

- **"oauth param not configured"** → Missing `NEXT_PUBLIC_COGNITO_DOMAIN`
- **"Google (Not Configured)" button** → Missing environment variable
- **OAuth redirect fails** → Check redirect URIs in Google Console

## 🚀 Current Status

✅ **Auth forms working** (sign in, sign up, verify)  
✅ **Router errors fixed**  
✅ **Hydration errors fixed**  
❌ **Google OAuth** (needs environment variable)

Once you add the `NEXT_PUBLIC_COGNITO_DOMAIN`, Google OAuth will work perfectly!
