// lib/session.ts

'use server';

import { JWTPayload, SignJWT, jwtVerify } from 'jose';
import { cookies } from 'next/headers';
import { randomBytes } from 'crypto';
import { SessionPayload, CSRFToken, UserSessionData, CognitoTokens } from '@/types/auth';
import { getRedisClient, isRedisHealthy } from './redis';

// Session configuration
const SESSION_SECRET = new TextEncoder().encode(
  process.env.SESSION_SECRET || 'fallback-secret-key-change-in-production',
);
// Use secure cookie names only in production, simple names in development
const SESSION_COOKIE_NAME = process.env.NODE_ENV === 'production' ? '__Secure-session' : 'session';
const CSRF_COOKIE_NAME = process.env.NODE_ENV === 'production' ? '__Host-csrf-token' : 'csrf-token';
const ACCESS_TOKEN_COOKIE_NAME =
  process.env.NODE_ENV === 'production' ? '__Secure-access-token' : 'access-token';
const ID_TOKEN_COOKIE_NAME =
  process.env.NODE_ENV === 'production' ? '__Secure-id-token' : 'id-token';
const SESSION_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days
const CSRF_TOKEN_LENGTH = 32;

// Redis key prefixes for serverless session storage
const REDIS_SESSION_PREFIX = 'session:';
const REDIS_COGNITO_PREFIX = 'cognito:';
const REDIS_CSRF_PREFIX = 'csrf:';

// Session storage mode: 'redis' for serverless, 'cookies' for traditional
const STORAGE_MODE = process.env.SESSION_STORAGE_MODE || 'redis'; // Default to Redis for serverless

// Session and CSRF interfaces now imported from @/types/auth

/**
 * Secure cookie configuration with all security flags
 */
const getSecureCookieOptions = (maxAge?: number) => ({
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict' as const,
  maxAge: maxAge || SESSION_DURATION / 1000,
  path: '/',
  ...(process.env.NODE_ENV === 'production' && {
    domain: process.env.COOKIE_DOMAIN, // Set your domain in production
  }),
});

/**
 * Generate a cryptographically secure random token
 */
function generateSecureToken(length: number = CSRF_TOKEN_LENGTH): string {
  return randomBytes(length).toString('hex');
}

/**
 * Check if Redis is available for session storage
 */
async function useRedisStorage(): Promise<boolean> {
  if (STORAGE_MODE === 'cookies') {
    return false;
  }

  try {
    return await isRedisHealthy();
  } catch {
    return false;
  }
}

/**
 * Store data in Redis with TTL
 */
async function setRedisData(key: string, data: any, ttlSeconds: number): Promise<void> {
  try {
    const redis = getRedisClient();
    const serializedData = JSON.stringify(data);
    await redis.setex(key, ttlSeconds, serializedData);
    console.log(`🔐 [REDIS-SESSION] Data stored: ${key} (TTL: ${ttlSeconds}s)`);
  } catch (error) {
    console.error(`❌ [REDIS-SESSION] Failed to store data ${key}:`, error);
    throw error;
  }
}

/**
 * Get data from Redis
 */
async function getRedisData(key: string): Promise<any | null> {
  try {
    const redis = getRedisClient();
    const data = await redis.get(key);
    if (!data) {
      return null;
    }
    return JSON.parse(data);
  } catch (error) {
    console.error(`❌ [REDIS-SESSION] Failed to get data ${key}:`, error);
    return null;
  }
}

/**
 * Remove data from Redis
 */
async function deleteRedisData(key: string): Promise<void> {
  try {
    const redis = getRedisClient();
    await redis.del(key);
    console.log(`🗑️ [REDIS-SESSION] Data removed: ${key}`);
  } catch (error) {
    console.error(`❌ [REDIS-SESSION] Failed to remove data ${key}:`, error);
  }
}

/**
 * Create a session with Redis or JWT storage
 */
export async function createSession(userData: UserSessionData): Promise<string> {
  const now = Date.now();
  const exp = now + SESSION_DURATION;
  const sessionId = generateSecureToken(16); // Unique session ID

  const sessionData: SessionPayload = {
    username: userData.username,
    email: userData.email,
    userId: userData.userId,
    signedIn: true,
    iat: Math.floor(now / 1000),
    exp: Math.floor(exp / 1000),
    jti: sessionId,
  };

  // Try Redis storage first for serverless compatibility
  if (await useRedisStorage()) {
    const redisKey = `${REDIS_SESSION_PREFIX}${sessionId}`;
    const ttlSeconds = Math.floor(SESSION_DURATION / 1000);

    try {
      await setRedisData(redisKey, sessionData, ttlSeconds);
      console.log(`✅ [SESSION] Session created in Redis: ${sessionId}`);
      return sessionId; // Return session ID for Redis mode
    } catch (error) {
      console.warn('⚠️ [SESSION] Redis storage failed, falling back to JWT:', error);
    }
  }

  // Fallback to JWT for traditional cookie-based sessions
  console.log(`✅ [SESSION] Session created as JWT: ${sessionId}`);
  return await new SignJWT(sessionData as unknown as JWTPayload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(sessionData.exp)
    .setJti(sessionId)
    .sign(SESSION_SECRET);
}

/**
 * Verify session from Redis or JWT token
 */
export async function verifySession(token: string): Promise<SessionPayload | null> {
  // Try Redis storage first (token is session ID in Redis mode)
  if (await useRedisStorage()) {
    try {
      const redisKey = `${REDIS_SESSION_PREFIX}${token}`;
      const sessionData = await getRedisData(redisKey);

      if (sessionData) {
        // Check if session is expired
        if (sessionData.exp && Date.now() / 1000 > sessionData.exp) {
          console.warn('⚠️ [SESSION] Redis session expired:', token);
          await deleteRedisData(redisKey);
          return null;
        }

        console.log(`✅ [SESSION] Session verified from Redis: ${token}`);
        return sessionData as SessionPayload;
      }
    } catch (error) {
      console.warn('⚠️ [SESSION] Redis verification failed, trying JWT:', error);
    }
  }

  // Fallback to JWT verification
  try {
    const { payload } = await jwtVerify(token, SESSION_SECRET);

    // Validate payload structure
    if (
      typeof payload.username === 'string' &&
      typeof payload.email === 'string' &&
      typeof payload.signedIn === 'boolean' &&
      typeof payload.jti === 'string' &&
      payload.signedIn === true
    ) {
      console.log(`✅ [SESSION] Session verified from JWT: ${payload.jti}`);
      return payload as unknown as SessionPayload;
    }

    return null;
  } catch (error) {
    console.error('❌ [SESSION] Session verification failed:', error);
    return null;
  }
}

/**
 * Get current session from cookies
 */
export async function getSession(): Promise<SessionPayload | null> {
  try {
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get(SESSION_COOKIE_NAME);

    if (!sessionCookie?.value) {
      return null;
    }

    return await verifySession(sessionCookie.value);
  } catch (error) {
    console.error('Session retrieval error:', error);
    return null;
  }
}

/**
 * Set session cookie or store session ID for Redis mode
 */
export async function setSessionCookie(sessionToken: string): Promise<void> {
  const cookieStore = await cookies();

  // In Redis mode, we store a lightweight session ID in the cookie
  // In JWT mode, we store the full JWT token
  cookieStore.set(SESSION_COOKIE_NAME, sessionToken, {
    ...getSecureCookieOptions(),
  });

  console.log(`🍪 [SESSION] Session cookie set: ${sessionToken.substring(0, 10)}...`);
}

/**
 * Generate and set CSRF token
 */
export async function generateCSRFToken(): Promise<string> {
  const token = generateSecureToken();
  const exp = Date.now() + 60 * 60 * 1000; // 1 hour expiry

  const csrfData: CSRFToken = { token, exp };
  const cookieStore = await cookies();

  cookieStore.set(CSRF_COOKIE_NAME, JSON.stringify(csrfData), {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 60 * 60, // 1 hour
    path: '/',
  });

  return token;
}

/**
 * Verify CSRF token from request
 */
export async function verifyCSRFToken(providedToken: string): Promise<boolean> {
  try {
    const cookieStore = await cookies();
    const csrfCookie = cookieStore.get(CSRF_COOKIE_NAME);

    if (!csrfCookie?.value || !providedToken) {
      return false;
    }

    const csrfData: CSRFToken = JSON.parse(csrfCookie.value);

    // Check token match and expiry
    if (csrfData.token === providedToken && Date.now() < csrfData.exp) {
      return true;
    }

    return false;
  } catch (error) {
    console.error('CSRF token verification failed:', error);
    return false;
  }
}

/**
 * Clear all session and security cookies
 */
export async function clearSession(sessionId?: string): Promise<void> {
  const cookieStore = await cookies();

  console.log('🧹 [SESSION] Clearing all session and token data...');

  // Clear Redis data if available and session ID provided
  if ((await useRedisStorage()) && sessionId) {
    try {
      const sessionKey = `${REDIS_SESSION_PREFIX}${sessionId}`;
      const cognitoKey = `${REDIS_COGNITO_PREFIX}${sessionId}`;

      await Promise.all([deleteRedisData(sessionKey), deleteRedisData(cognitoKey)]);

      console.log(`🗑️ [SESSION] Redis data cleared for session: ${sessionId}`);
    } catch (error) {
      console.warn('⚠️ [SESSION] Failed to clear Redis data:', error);
    }
  }

  // Clear session cookie
  cookieStore.delete(SESSION_COOKIE_NAME);

  // Clear CSRF token
  cookieStore.delete(CSRF_COOKIE_NAME);

  // Clear Cognito token cookies
  cookieStore.delete(ACCESS_TOKEN_COOKIE_NAME);
  cookieStore.delete(ID_TOKEN_COOKIE_NAME);

  // Clear any legacy cookies
  cookieStore.delete('amplify-auth-token');
  cookieStore.delete('temp-signup-password');

  console.log('🧹 [SESSION] All session data cleared');
}

/**
 * Refresh session if needed (extend expiry)
 */
export async function refreshSession(currentSession: SessionPayload): Promise<string | null> {
  // Refresh if session will expire in next 24 hours
  const timeUntilExpiry = currentSession.exp * 1000 - Date.now();
  const shouldRefresh = timeUntilExpiry < 24 * 60 * 60 * 1000;

  if (shouldRefresh) {
    return await createSession({
      username: currentSession.username,
      email: currentSession.email,
      userId: currentSession.userId,
    });
  }

  return null;
}

/**
 * Validate session and optionally refresh
 */
export async function validateAndRefreshSession(): Promise<SessionPayload | null> {
  const session = await getSession();

  if (!session) {
    return null;
  }

  // Try to refresh if needed
  const newToken = await refreshSession(session);
  if (newToken) {
    await setSessionCookie(newToken);
    // Return updated session data
    return await verifySession(newToken);
  }

  return session;
}

/**
 * Set Cognito tokens in Redis or cookies based on storage mode
 */
export async function setCognitoTokenCookies(
  tokens: CognitoTokens,
  sessionId?: string,
): Promise<void> {
  // Try Redis storage first for serverless compatibility
  if ((await useRedisStorage()) && sessionId) {
    try {
      const redisKey = `${REDIS_COGNITO_PREFIX}${sessionId}`;
      const ttlSeconds = 60 * 60; // 1 hour TTL

      await setRedisData(redisKey, tokens, ttlSeconds);
      console.log(`🔐 [TOKENS] Cognito tokens stored in Redis for session: ${sessionId}`);
      return;
    } catch (error) {
      console.warn('⚠️ [TOKENS] Redis storage failed, falling back to cookies:', error);
    }
  }

  // Fallback to cookie storage
  const cookieStore = await cookies();
  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict' as const,
    maxAge: 60 * 60, // 1 hour for access/id tokens
    path: '/',
  };

  console.log('🍪 [TOKENS] Setting Cognito token cookies...');

  if (tokens.accessToken) {
    cookieStore.set(ACCESS_TOKEN_COOKIE_NAME, tokens.accessToken, cookieOptions);
    console.log('🍪 [TOKENS] Access token cookie set');
  }

  if (tokens.idToken) {
    cookieStore.set(ID_TOKEN_COOKIE_NAME, tokens.idToken, cookieOptions);
    console.log('🍪 [TOKENS] ID token cookie set');
  }
}

/**
 * Get Cognito tokens from Redis or cookies based on storage mode
 */
export async function getCognitoTokenCookies(sessionId?: string): Promise<CognitoTokens> {
  // Try Redis storage first if session ID is available
  if ((await useRedisStorage()) && sessionId) {
    try {
      const redisKey = `${REDIS_COGNITO_PREFIX}${sessionId}`;
      const tokens = await getRedisData(redisKey);

      if (tokens) {
        console.log(`🔐 [TOKENS] Retrieved Cognito tokens from Redis for session: ${sessionId}`);
        return tokens as CognitoTokens;
      }
    } catch (error) {
      console.warn('⚠️ [TOKENS] Redis retrieval failed, trying cookies:', error);
    }
  }

  // Fallback to cookie storage
  try {
    const cookieStore = await cookies();

    const accessToken = cookieStore.get(ACCESS_TOKEN_COOKIE_NAME)?.value;
    const idToken = cookieStore.get(ID_TOKEN_COOKIE_NAME)?.value;

    console.log('🍪 [TOKENS] Retrieved Cognito tokens from cookies:', {
      hasAccessToken: !!accessToken,
      hasIdToken: !!idToken,
    });

    return {
      accessToken,
      idToken,
    };
  } catch (error) {
    console.error('❌ [TOKENS] Failed to get Cognito tokens:', error);
    return {};
  }
}

/**
 * Clear Cognito token cookies
 */
export async function clearCognitoTokenCookies(): Promise<void> {
  const cookieStore = await cookies();

  console.log('🍪 [TOKENS] Clearing Cognito token cookies...');

  cookieStore.delete(ACCESS_TOKEN_COOKIE_NAME);
  cookieStore.delete(ID_TOKEN_COOKIE_NAME);

  console.log('🍪 [TOKENS] Cognito token cookies cleared');
}

/**
 * Get current session ID from cookie
 */
export async function getCurrentSessionId(): Promise<string | null> {
  try {
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get(SESSION_COOKIE_NAME);
    return sessionCookie?.value || null;
  } catch (error) {
    console.error('❌ [SESSION] Failed to get session ID:', error);
    return null;
  }
}

/**
 * Get user email from ID token with session-aware retrieval
 */
export async function getUserEmailFromToken(): Promise<string | null> {
  try {
    const sessionId = await getCurrentSessionId();
    const tokens = await getCognitoTokenCookies(sessionId || undefined);

    if (tokens.idToken) {
      // Decode JWT payload (note: this is not secure validation, just for extracting email)
      const payload = JSON.parse(atob(tokens.idToken.split('.')[1]));
      return payload.email || null;
    }

    return null;
  } catch (error) {
    console.error('❌ [TOKENS] Error extracting email from ID token:', error);
    return null;
  }
}
