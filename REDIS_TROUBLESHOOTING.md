# Redis Connection Troubleshooting Guide

## 🚨 Common Issues & Solutions

### Issue: Connection Reset (ECONNRESET)

**Symptoms:**
```
❌ [REDIS] Redis connection error: {
  message: 'read ECONNRESET',
  code: 'ECONNRESET',
  errno: -54
}
```

**Causes & Solutions:**

1. **Redis Server Not Running**
   ```bash
   # Check if Redis is running
   ps aux | grep redis
   
   # Start Redis (macOS with Homebrew)
   brew services start redis
   
   # Start Redis (Ubuntu/Debian)
   sudo systemctl start redis-server
   
   # Start Redis with Docker
   docker run -d --name redis -p 6379:6379 redis:7-alpine
   ```

2. **Redis Configuration Issues**
   ```bash
   # Check Redis configuration
   redis-cli config get "*"
   
   # Check timeout settings
   redis-cli config get timeout
   
   # Set timeout to 0 (disable) if needed
   redis-cli config set timeout 0
   ```

3. **Network/Firewall Issues**
   ```bash
   # Test direct connection
   telnet localhost 6379
   
   # Check port availability
   netstat -tlnp | grep 6379
   
   # Test Redis CLI
   redis-cli ping
   ```

### Issue: Max Retries Reached

**Symptoms:**
```
❌ [REDIS] Redis connection error: {
  message: 'Reached the max retries per request limit (which is 3)'
}
```

**Solutions:**

1. **Reduce Retry Attempts** (already implemented)
   ```javascript
   maxRetriesPerRequest: 2  // Reduced from 3
   ```

2. **Check Redis Memory**
   ```bash
   # Check Redis memory usage
   redis-cli info memory
   
   # Check if Redis is out of memory
   redis-cli config get maxmemory
   ```

3. **Restart Redis**
   ```bash
   # Docker
   docker restart redis
   
   # System service
   sudo systemctl restart redis-server
   
   # Homebrew
   brew services restart redis
   ```

## 🧪 Testing & Diagnostics

### 1. Quick Redis Test

```bash
# Test Redis connection directly
node scripts/test-redis.js
```

### 2. Application Health Check

```bash
# Test application Redis integration
curl http://localhost:3000/api/health/redis
```

### 3. Session Storage Test

```bash
# Test serverless session storage
curl http://localhost:3000/api/debug/session-storage
```

### 4. Manual Redis Commands

```bash
# Connect to Redis CLI
redis-cli

# Test basic commands
127.0.0.1:6379> ping
PONG

127.0.0.1:6379> set test "hello"
OK

127.0.0.1:6379> get test
"hello"

127.0.0.1:6379> del test
(integer) 1

# Check server info
127.0.0.1:6379> info server
```

## 🔧 Configuration Fixes

### 1. Environment Variables

```bash
# .env.local
REDIS_URL=redis://localhost:6379
SESSION_STORAGE_MODE=redis
SESSION_SECRET=your-secure-session-secret
```

### 2. Redis Server Configuration

```bash
# /etc/redis/redis.conf (or /usr/local/etc/redis.conf on macOS)

# Disable timeout (prevents connection drops)
timeout 0

# Increase TCP keepalive
tcp-keepalive 60

# Set max memory policy
maxmemory-policy allkeys-lru

# Enable persistence
save 900 1
save 300 10
save 60 10000
```

### 3. Docker Redis Setup

```yaml
# docker-compose.yml
version: '3.8'
services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: >
      redis-server
      --timeout 0
      --tcp-keepalive 60
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  redis_data:
```

## 🚀 Quick Fixes

### Option 1: Use Cookie Fallback Mode

```bash
# Temporarily disable Redis mode
echo "SESSION_STORAGE_MODE=cookies" >> .env.local
```

### Option 2: Reset Redis

```bash
# Stop all Redis processes
sudo pkill redis-server

# Clear Redis data (if needed)
rm -rf /var/lib/redis/*  # Ubuntu
rm -rf /usr/local/var/db/redis/*  # macOS

# Restart Redis
redis-server
```

### Option 3: Use Docker Redis

```bash
# Remove existing Redis container
docker rm -f redis

# Start fresh Redis container
docker run -d --name redis -p 6379:6379 \
  -v redis_data:/data \
  redis:7-alpine redis-server --timeout 0 --tcp-keepalive 60
```

## 📊 Monitoring

### Redis Logs

```bash
# Docker logs
docker logs redis -f

# System logs (Ubuntu)
tail -f /var/log/redis/redis-server.log

# Homebrew logs (macOS)
tail -f /usr/local/var/log/redis.log
```

### Application Logs

```bash
# Watch for Redis connection events
npm run dev | grep REDIS
```

## 🆘 Emergency Fallback

If Redis continues to have issues, you can temporarily disable it:

```bash
# 1. Set cookie mode
export SESSION_STORAGE_MODE=cookies

# 2. Or comment out Redis URL
# REDIS_URL=redis://localhost:6379

# 3. Restart the application
npm run dev
```

The application will automatically fall back to JWT cookie-based sessions.

## 📞 Getting Help

1. **Check Redis status**: `redis-cli ping`
2. **Run diagnostics**: `node scripts/test-redis.js`
3. **Check application health**: `curl localhost:3000/api/health/redis`
4. **Review logs**: Look for ECONNRESET or timeout errors
5. **Try fallback mode**: Set `SESSION_STORAGE_MODE=cookies`
