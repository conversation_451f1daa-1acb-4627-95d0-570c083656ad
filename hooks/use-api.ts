// React Query hooks that directly use our internal API gateway routes
// Eliminates the intermediate api-calls layer for cleaner architecture
import { useQuery, useMutation } from '@tanstack/react-query';
import type { DeviceData, DeviceCardProps } from '@/types/device';
import type { JobsRowProps } from '@/types/jobs';
import type { TeamMember } from '@/types/team';
import type { ActionLogRowProps } from '@/types/logs';

// Internal API client
const apiClient = async (url: string, options?: RequestInit) => {
  const response = await fetch(url, {
    headers: { 'Content-Type': 'application/json' },
    ...options,
  });

  if (!response.ok) {
    throw new Error(`API Error: ${response.status} ${response.statusText}`);
  }

  return response.json();
};

/**
 * Fetch all quantum devices via API gateway
 * Supports filtering by provider, type, status, and availability
 */
export const useAllDevices = () =>
  useQuery<DeviceCardProps[]>({
    queryKey: ['devices'],
    queryFn: () => apiClient('/api/quantum-devices'),
  });

/**
 * Fetch a single quantum device by ID via API gateway
 */
export const useDeviceData = (deviceId: string) =>
  useQuery<DeviceData>({
    queryKey: ['device', deviceId],
    queryFn: () => apiClient(`/api/quantum-devices?qbraid_id=${deviceId}`),
    enabled: Boolean(deviceId),
  });

/**
 * Fetch quantum jobs for a specific device and provider via API gateway
 */
export const useJobsForDevice = (
  provider: string,
  device: string,
  page?: number,
  resultsPerPage?: number,
) =>
  useQuery<{ jobsArray: JobsRowProps[]; total: number }>({
    queryKey: ['jobs', provider, device, page, resultsPerPage],
    queryFn: () => {
      const params = new URLSearchParams({
        provider,
        qbraidDeviceId: device,
        page: (page ?? 0).toString(),
        resultsPerPage: (resultsPerPage ?? 10).toString(),
      });
      return apiClient(`/api/quantum-jobs/all-by-provider?${params.toString()}`);
    },
    enabled: Boolean(provider && device),
  });

/**
 * Update quantum device data via API gateway (mutation)
 */
export const useUpdateDeviceData = () =>
  useMutation<DeviceData, Error, { deviceId: string; postBody: any }>({
    mutationFn: ({ deviceId, postBody }) =>
      apiClient(`/api/quantum-devices/edit?id=${deviceId}`, {
        method: 'PUT',
        body: JSON.stringify(postBody),
      }),
  });

/**
 * Fetch organization information via API gateway
 */
export const useOrgInfo = (orgID: string) =>
  useQuery({
    queryKey: ['org', orgID],
    queryFn: () => apiClient(`/api/orgs/get/${orgID}`),
    enabled: Boolean(orgID),
  });

/**
 * Fetch users in an organization via API gateway
 * Note: Date formatting is now handled server-side
 */
export const useOrgUsers = (orgID: string, page: number, pageSize: number) =>
  useQuery<{ users: TeamMember[]; totalUsers: number }>({
    queryKey: ['orgUsers', orgID, page, pageSize],
    queryFn: () => apiClient(`/api/orgs/users/${orgID}/${page}/${pageSize}`),
    enabled: Boolean(orgID),
  });

/**
 * Add user to organization via API gateway (mutation)
 */
export const useInviteUser = () =>
  useMutation({
    mutationFn: (body: { email: string; orgId: string; role?: string }) =>
      apiClient('/api/orgs/users/add', {
        method: 'POST',
        body: JSON.stringify(body),
      }),
  });

/**
 * Update user role in organization via API gateway (mutation)
 */
export const useUpdateUserRole = () =>
  useMutation({
    mutationFn: (body: { email: string; orgId: string; role: string }) =>
      apiClient('/api/orgs/users/update', {
        method: 'POST',
        body: JSON.stringify(body),
      }),
  });

/**
 * Remove user from organization via API gateway (mutation)
 */
export const useRemoveUser = () =>
  useMutation({
    mutationFn: (body: { email: string; orgId: string }) =>
      apiClient('/api/orgs/users/remove', {
        method: 'POST',
        body: JSON.stringify(body),
      }),
  });

/**
 * Legacy hook for backward compatibility - use specific hooks above instead
 * @deprecated Use useInviteUser, useUpdateUserRole, or useRemoveUser instead
 */
export const useManipulateUsers = () =>
  useMutation({
    mutationFn: ({ action, body }: { action: string; body: any }) => {
      const endpoint =
        action === 'invite'
          ? '/api/orgs/users/add'
          : action === 'changeRole'
            ? '/api/orgs/users/update'
            : action === 'remove'
              ? '/api/orgs/users/remove'
              : null;

      if (!endpoint) throw new Error(`Invalid action: ${action}`);

      return apiClient(endpoint, {
        method: 'POST',
        body: JSON.stringify(body),
      });
    },
  });

/**
 * Fetch audit logs via API gateway
 */
export const useActionLogs = (provider: string, page: number, resultsPerPage: number) =>
  useQuery<{ auditLogsArray: ActionLogRowProps[] }>({
    queryKey: ['actionLogs', provider, page, resultsPerPage],
    queryFn: () =>
      apiClient(`/api/audit-logs/${provider}?page=${page}&resultsPerPage=${resultsPerPage}`),
    enabled: Boolean(provider),
  });

/**
 * Submit an audit log entry via API gateway (mutation)
 */
export const useSubmitActionLog = () =>
  useMutation({
    mutationFn: (params: {
      provider: string;
      email: string;
      role: string;
      type: string;
      deviceId?: string;
      otherUserEmail?: string;
      otherUserRole?: string;
    }) =>
      apiClient('/api/audit-logs', {
        method: 'POST',
        body: JSON.stringify(params),
      }),
  });
